# PropBolt Production Deployment Guide

This guide covers the complete production deployment of PropBolt's comprehensive land search platform with dual API integration, NextAuth authentication, and automatic SSL certificate provisioning.

## 📋 Architecture Overview

### **Multi-Service Architecture**
- **Google Cloud CLI** installed and authenticated

- **Frontend Service** (Next.js) - `propbolt.com`
- **Backend Service** (Go API) - `brain.propbolt.com`, `api.propbolt.com`
- **Database** - Google Cloud SQL PostgreSQL
- **SSL Certificates** - Automatic provisioning for all domains

### **Dual API Integration**
- **RealEstateAPI.com** - Professional real estate data (9 endpoints)
- **PropBolt Internal API** - Legacy Zillow integration with proxy rotation


# 🔧 Prerequisites

1. **Google Cloud CLI** installed and authenticated
2. **Node.js 20+** and **Go 1.22+**
3. **Project Access** to `gold-braid-458901-v2`
4. **Domain Configuration** for all PropBolt domains


## 🏗️ Quick Deployment

### **One-Command Production Deployment**

This script automatically:
- ✅ Builds and deploys Go backend
- ✅ Builds and deploys Next.js frontend  
- ✅ Configures domain routing
- ✅ Provisions SSL certificates for all domains
- ✅ Sets up monitoring and logging
- ✅ Configures firewall rules
- ✅ Tests deployment health

## 🌐 Domain Configuration

### **Production Domains**
- `propbolt.com` → Frontend (Next.js)
- `brain.propbolt.com` → Admin API (Go)
- `api.propbolt.com` → User API (Go)
- `admin.propbolt.com` → Admin redirect (Go)
- `go.propbolt.com` → User redirect (Go)

### **SSL Certificates**
All domains automatically receive SSL certificates via Google App Engine managed certificates.

## 🔐 Authentication System

### **NextAuth.js Integration**
Login, Sign Up, Forgot Password

### **Account Types**
- `admin` - Access to land search dashboard and user management
- `user` - Access to data internal GoLang API
- `suspend` - Login prevented
- `null` - Login prevented

## 🔍 Comprehensive Land Search Features

### **Phase 1: Dual API Integration** ✅
- **Property Discovery** - Multi-source search results
- **Financial Analysis** - Valuation and comparables
- **Legal Research** - Liens and owner information
- **Location Intelligence** - Maps and proximity data

### **Phase 2: Advanced Analysis** ✅
- **Development Potential** - Zoning and buildability
- **Investment Analysis** - ROI and market trends
- **Risk Assessment** - Environmental and legal risks
- **Utilities Assessment** - Infrastructure availability

### **Phase 3: User Experience** ✅
- **Interactive Dashboard** - Real-time search and filtering
- **Property Analysis Modal** - Comprehensive property reports
- **Map Integration** - Google Maps with property pins
- **Responsive Design** - Mobile and desktop optimized

## 📊 API Endpoints

### **Land Search API**
```
POST /api/land-search
GET /api/land-search?location=Daytona Beach, FL
```

### **Property Analysis API**
```
POST /api/property-analysis
```

### **Authentication API**
```
POST /api/auth/user
GET /api/auth/[...nextauth]
```

### **Health Check API**
```
GET /api/health
HEAD /api/health
```

## 🗄️ Database Configuration

### **Google Cloud SQL PostgreSQL**
- **Instance**: `propbolt-postgres`
- **Database**: `propbolt`
- **Connection**: SSL required
- **Backup**: Automatic daily backups

### **User Schema**
```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  username VARCHAR(255) NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  role VARCHAR(50) DEFAULT 'user',
  account_type VARCHAR(50), -- 'land', 'data', or NULL
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 📈 Monitoring and Logging

### **Google Cloud Monitoring**
- **Application Metrics** - Performance and usage
- **Error Tracking** - Real-time error monitoring
- **Uptime Monitoring** - Service availability

### **Logging Configuration**
- **Application Logs** - Structured JSON logging
- **Access Logs** - Request/response tracking
- **Error Logs** - Exception and error tracking


## 🔄 Rollback Procedures

- **SSL Issues**: Certificates can take up to 15 minutes to provision
- **Domain Mapping**: Verify DNS settings point to Google App Engine
- **Database Connection**: Check Cloud SQL instance status and firewall rules
- **API Errors**: Review application logs for detailed error messages

## 🎉 Success Metrics

After successful deployment, you should have:
- ✅ **Secure HTTPS** access to all domains
- ✅ **Dual API integration** working seamlessly
- ✅ **Authentication system** protecting all routes
- ✅ **Comprehensive land search** with Zillow-style features
- ✅ **Production monitoring** and logging active
- ✅ **Auto-scaling** and load balancing configured

---



## 🏗️ Quick Deployment

### **One-Command Production Deployment**



This script automatically:
- ✅ Builds and deploys Go backend
- ✅ Builds and deploys Next.js frontend  
- ✅ Configures domain routing
- ✅ Provisions SSL certificates for all domains
- ✅ Sets up monitoring and logging
- ✅ Configures firewall rules
- ✅ Tests deployment health

## 🌐 Domain Configuration

### **Production Domains**
- `propbolt.com` → Frontend (Next.js)
- `brain.propbolt.com` → Admin API (Go)
- `api.propbolt.com` → User API (Go)
- `admin.propbolt.com` → Admin redirect (Go)
- `go.propbolt.com` → User redirect (Go)

### **SSL Certificates**
All domains automatically receive SSL certificates via Google App Engine managed certificates.

## 🔐 Authentication System

### **NextAuth.js Integration**
Login, Sign Up, Forgot Password

### **Account Types**
- `admin` - Access to land search dashboard and user management
- `user` - Access to data internal GoLang API
- `suspend` - Login prevented
- `null` - Login prevented

## 🔍 Comprehensive Land Search Features

### **Phase 1: Dual API Integration** ✅
- **Property Discovery** - Multi-source search results
- **Financial Analysis** - Valuation and comparables
- **Legal Research** - Liens and owner information
- **Location Intelligence** - Maps and proximity data

### **Phase 2: Advanced Analysis** ✅
- **Development Potential** - Zoning and buildability
- **Investment Analysis** - ROI and market trends
- **Risk Assessment** - Environmental and legal risks
- **Utilities Assessment** - Infrastructure availability

### **Phase 3: User Experience** ✅
- **Interactive Dashboard** - Real-time search and filtering
- **Property Analysis Modal** - Comprehensive property reports
- **Map Integration** - Google Maps with property pins
- **Responsive Design** - Mobile and desktop optimized

## 📊 API Endpoints

### **Land Search API**
```
POST /api/land-search
GET /api/land-search?location=Daytona Beach, FL
```

### **Property Analysis API**
```
POST /api/property-analysis
```

### **Authentication API**
```
POST /api/auth/user
GET /api/auth/[...nextauth]
```

### **Health Check API**
```
GET /api/health
HEAD /api/health
```


## 📈 Monitoring and Logging

### **Google Cloud Monitoring**
- **Application Metrics** - Performance and usage
- **Error Tracking** - Real-time error monitoring
- **Uptime Monitoring** - Service availability

### **Logging Configuration**
- **Application Logs** - Structured JSON logging
- **Access Logs** - Request/response tracking
- **Error Logs** - Exception and error tracking

## 🎉 Success Metrics

After successful deployment, you should have:
- ✅ **Secure HTTPS** access to all domains
- ✅ **Dual API integration** working seamlessly
- ✅ **Authentication system** protecting all routes
- ✅ **Comprehensive land search** with Zillow-style features
- ✅ **Production monitoring** and logging active
- ✅ **Auto-scaling** and load balancing configured

---