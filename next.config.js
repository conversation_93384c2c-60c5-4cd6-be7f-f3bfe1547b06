/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      'propbolt.com',
      'api.mapbox.com'
    ],
  },
  env: {
    NEXT_PUBLIC_API_BASE_URL: 'https://propbolt.com',
    NEXT_PUBLIC_MAPBOX_TOKEN: 'pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY21icXYxbjZnMDN3czJrb2h5djd1bng0OSJ9.79dGToNOI5onemlC19dcDw'
  },
  async rewrites() {
    // API routing to propbolt.com backend
    const apiBaseUrl = 'https://propbolt.com';

    return [
      {
        source: '/api/:path*',
        destination: `${apiBaseUrl}/api/:path*`,
      },
    ];
  },
}

module.exports = nextConfig
