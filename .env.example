# PropBolt Brain API - Environment Configuration Template
# Copy this file to .env and update with your production credentials

# Core Application Configuration
NODE_ENV=production
PORT=8080

# Database Configuration - Google Cloud SQL PostgreSQL
# For production: Use Cloud SQL socket connection
# For development: Use proxy connection with localhost
DATABASE_URL=postgresql://propbolt_user:YOUR_PASSWORD@localhost:5432/propbolt?sslmode=disable

# Real Estate API Configuration
REAL_ESTATE_API_KEY=YOUR_REAL_ESTATE_API_KEY

# Proxy Configuration (Smartproxy endpoints)
# Comma-separated list of proxy URLs in format: *****************************:port
PROXY_URLS=http://sp0o8xf1er:<EMAIL>:10001,http://sp0o8xf1er:<EMAIL>:10002,http://sp0o8xf1er:<EMAIL>:10003,http://sp0o8xf1er:<EMAIL>:10004,http://sp0o8xf1er:<EMAIL>:10005

# Domain Configuration
PRODUCTION_DOMAIN=propbolt.com

# Frontend Environment Variables (for Next.js)
NEXT_PUBLIC_API_BASE_URL=https://propbolt.com
NEXT_PUBLIC_MAPBOX_TOKEN=YOUR_MAPBOX_TOKEN
NEXT_PUBLIC_REAL_ESTATE_API_KEY=YOUR_REAL_ESTATE_API_KEY
NEXT_PUBLIC_REAL_ESTATE_API_URL=https://api.realestateapi.com/v2/
NEXT_PUBLIC_APP_NAME=Vacant Land Search
NEXT_PUBLIC_APP_DESCRIPTION=Professional real estate admin panel for vacant land search in Daytona Beach, Florida

# Authentication Configuration (NextAuth.js)
NEXTAUTH_URL=https://propbolt.com
NEXTAUTH_SECRET=YOUR_NEXTAUTH_SECRET_KEY

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=gold-braid-458901-v2
GOOGLE_CLOUD_SQL_INSTANCE=propbolt-postgres

# Application Features
DEMO_MODE=false

# Instructions:
# 1. Copy this file: cp .env.example .env
# 2. Update all YOUR_* placeholders with actual values
# 3. For production deployment, ensure DATABASE_URL uses Cloud SQL socket connection
# 4. For local development, use localhost connection with SSL disabled
