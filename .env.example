# PropBolt Brain API - Environment Configuration Template
# Copy this file to .env and update with your production credentials

# Core Application Configuration
NODE_ENV=production
PORT=8080

# Database Configuration - Google Cloud SQL PostgreSQL
# For production: Use Cloud SQL socket connection
# For development: Use proxy connection with localhost
DATABASE_URL=

# Real Estate API Configuration
REAL_ESTATE_API_KEY=

# Proxy Configuration (Smartproxy endpoints)
# Comma-separated list of proxy URLs in format: *****************************:port
PROXY_URLS=

# Domain Configuration
PRODUCTION_DOMAIN=propbolt.com

# Frontend Environment Variables (for Next.js)
NEXT_PUBLIC_API_BASE_URL=https://propbolt.com
NEXT_PUBLIC_MAPBOX_TOKEN=
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=
NEXT_PUBLIC_REAL_ESTATE_API_KEY=
NEXT_PUBLIC_REAL_ESTATE_API_URL=https://api.realestateapi.com/v2/
NEXT_PUBLIC_APP_NAME=Vacant Land Search
NEXT_PUBLIC_APP_DESCRIPTION=Professional real estate admin panel for vacant land search in Daytona Beach, Florida

# Authentication Configuration (NextAuth.js)
NEXTAUTH_URL=https://propbolt.com
NEXTAUTH_SECRET=

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=gold-braid-458901-v2
GOOGLE_CLOUD_SQL_INSTANCE=propbolt-postgres

# Application Features
# All features use live production data from PropBolt API

# Instructions:
# 1. Copy this file: cp .env.example .env
# 2. Update all YOUR_* placeholders with actual values
# 3. For production deployment, ensure DATABASE_URL uses Cloud SQL socket connection
# 4. For local development, use localhost connection with SSL disabled
